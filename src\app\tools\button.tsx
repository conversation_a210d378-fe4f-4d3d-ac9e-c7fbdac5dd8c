import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, Moon } from 'lucide-react';
import './button-generator.css';

interface ButtonConfig {
  text: string;
  // Light mode colors
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  hoverBackgroundColor: string;
  hoverTextColor: string;
  hoverBorderColor: string;
  // Dark mode colors
  darkBackgroundColor: string;
  darkTextColor: string;
  darkBorderColor: string;
  darkHoverBackgroundColor: string;
  darkHoverTextColor: string;
  darkHoverBorderColor: string;
  // Other properties
  borderWidth: number;
  borderStyle: string;
  borderRadius: number;
  width: number;
  height: number;
  paddingX: number;
  paddingY: number;
  fontSize: number;
  fontWeight: string;
  boxShadow: string;
  transition: string;
  supportDarkMode: boolean;
}

const ButtonGenerator: React.FC = () => {
  const [config, setConfig] = useState<ButtonConfig>({
    text: 'Click Me',
    // Light mode colors
    backgroundColor: '#3b82f6',
    textColor: '#ffffff',
    borderColor: '#3b82f6',
    hoverBackgroundColor: '#2563eb',
    hoverTextColor: '#ffffff',
    hoverBorderColor: '#2563eb',
    // Dark mode colors
    darkBackgroundColor: '#1e40af',
    darkTextColor: '#f1f5f9',
    darkBorderColor: '#1e40af',
    darkHoverBackgroundColor: '#1d4ed8',
    darkHoverTextColor: '#f1f5f9',
    darkHoverBorderColor: '#1d4ed8',
    // Other properties
    borderWidth: 2,
    borderStyle: 'solid',
    borderRadius: 8,
    width: 140,
    height: 44,
    paddingX: 24,
    paddingY: 12,
    fontSize: 16,
    fontWeight: '500',
    boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)',
    transition: 'all 0.2s ease-in-out',
    supportDarkMode: true,
  });

  const [exportFormat, setExportFormat] = useState('css');
  const [tailwindFormat, setTailwindFormat] = useState<'className' | 'class'>('className');
  const [copied, setCopied] = useState(false);
  const [previewMode, setPreviewMode] = useState<'light' | 'dark'>('light');
  const [isGenerating, setIsGenerating] = useState(false);

  const updateConfig = (key: keyof ButtonConfig, value: string | number | boolean) => {
    setConfig(prev => ({ ...prev, [key]: value }));
  };

  const getButtonStyle = (isHover: boolean = false, isDark: boolean = false): React.CSSProperties => {
    const colors = isDark ? {
      backgroundColor: isHover ? config.darkHoverBackgroundColor : config.darkBackgroundColor,
      color: isHover ? config.darkHoverTextColor : config.darkTextColor,
      borderColor: isHover ? config.darkHoverBorderColor : config.darkBorderColor,
    } : {
      backgroundColor: isHover ? config.hoverBackgroundColor : config.backgroundColor,
      color: isHover ? config.hoverTextColor : config.textColor,
      borderColor: isHover ? config.hoverBorderColor : config.borderColor,
    };

    return {
      ...colors,
      borderWidth: `${config.borderWidth}px`,
      borderStyle: config.borderStyle,
      borderRadius: `${config.borderRadius}px`,
      width: `${config.width}px`,
      height: `${config.height}px`,
      paddingLeft: `${config.paddingX}px`,
      paddingRight: `${config.paddingX}px`,
      paddingTop: `${config.paddingY}px`,
      paddingBottom: `${config.paddingY}px`,
      fontSize: `${config.fontSize}px`,
      fontWeight: config.fontWeight,
      boxShadow: config.boxShadow,
      transition: config.transition,
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'inherit',
      outline: 'none',
    };
  };

  const generateCSS = (): string => {
    const lightStyle = getButtonStyle(false, false);
    const lightHoverStyle = getButtonStyle(true, false);
    const darkStyle = getButtonStyle(false, true);
    const darkHoverStyle = getButtonStyle(true, true);
    
    let css = `.custom-button {
  background-color: ${lightStyle.backgroundColor};
  color: ${lightStyle.color};
  border: ${lightStyle.borderWidth} ${lightStyle.borderStyle} ${lightStyle.borderColor};
  border-radius: ${lightStyle.borderRadius};
  width: ${lightStyle.width};
  height: ${lightStyle.height};
  padding: ${lightStyle.paddingTop} ${lightStyle.paddingRight} ${lightStyle.paddingBottom} ${lightStyle.paddingLeft};
  font-size: ${lightStyle.fontSize};
  font-weight: ${lightStyle.fontWeight};
  box-shadow: ${lightStyle.boxShadow};
  transition: ${lightStyle.transition};
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: inherit;
  outline: none;
}

.custom-button:hover {
  background-color: ${lightHoverStyle.backgroundColor};
  color: ${lightHoverStyle.color};
  border-color: ${lightHoverStyle.borderColor};
}`;

    if (config.supportDarkMode) {
      css += `

@media (prefers-color-scheme: dark) {
  .custom-button {
    background-color: ${darkStyle.backgroundColor};
    color: ${darkStyle.color};
    border-color: ${darkStyle.borderColor};
  }
  
  .custom-button:hover {
    background-color: ${darkHoverStyle.backgroundColor};
    color: ${darkHoverStyle.color};
    border-color: ${darkHoverStyle.borderColor};
  }
}`;
    }

    return css;
  };

  const generateTailwindCSS = (): string => {
    const getColorClass = (color: string, prefix: string = '') => {
      return `${prefix}[${color}]`;
    };

    const borderStyleClass = config.borderStyle === 'solid' ? 'border-solid' : 
                           config.borderStyle === 'dashed' ? 'border-dashed' : 
                           config.borderStyle === 'dotted' ? 'border-dotted' : 'border-solid';

    const fontWeightClass = config.fontWeight === '300' ? 'font-light' :
                           config.fontWeight === '400' ? 'font-normal' :
                           config.fontWeight === '500' ? 'font-medium' :
                           config.fontWeight === '600' ? 'font-semibold' :
                           config.fontWeight === '700' ? 'font-bold' : 'font-medium';

    let classes = [
      `bg-${getColorClass(config.backgroundColor)}`,
      `text-${getColorClass(config.textColor)}`,
      `border-[${config.borderWidth}px]`,
      `border-${getColorClass(config.borderColor)}`,
      borderStyleClass,
      `rounded-[${config.borderRadius}px]`,
      `w-[${config.width}px]`,
      `h-[${config.height}px]`,
      `px-[${config.paddingX}px]`,
      `py-[${config.paddingY}px]`,
      `text-[${config.fontSize}px]`,
      fontWeightClass,
      `hover:bg-${getColorClass(config.hoverBackgroundColor)}`,
      `hover:text-${getColorClass(config.hoverTextColor)}`,
      `hover:border-${getColorClass(config.hoverBorderColor)}`,
      'inline-flex',
      'items-center',
      'justify-center',
      'cursor-pointer',
      'outline-none',
      'transition-all',
      'duration-200',
      'ease-in-out'
    ];

    if (config.supportDarkMode) {
      classes = classes.concat([
        `dark:bg-${getColorClass(config.darkBackgroundColor)}`,
        `dark:text-${getColorClass(config.darkTextColor)}`,
        `dark:border-${getColorClass(config.darkBorderColor)}`,
        `dark:hover:bg-${getColorClass(config.darkHoverBackgroundColor)}`,
        `dark:hover:text-${getColorClass(config.darkHoverTextColor)}`,
        `dark:hover:border-${getColorClass(config.darkHoverBorderColor)}`
      ]);
    }

    const classString = classes.join(' ');
    const attributeName = tailwindFormat === 'className' ? 'className' : 'class';
    
    return `<button ${attributeName}="${classString}">
  ${config.text}
</button>`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      setIsGenerating(true);
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
        setIsGenerating(false);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
      setIsGenerating(false);
    }
  };

  const getExportContent = () => {
    switch (exportFormat) {
      case 'css':
        return generateCSS();
      case 'tailwind':
        return generateTailwindCSS();
      default:
        return generateCSS();
    }
  };

  const borderStyles = ['solid', 'dashed', 'dotted', 'double'];
  const fontWeights = [
    { value: '300', label: 'Light' },
    { value: '400', label: 'Normal' },
    { value: '500', label: 'Medium' },
    { value: '600', label: 'Semibold' },
    { value: '700', label: 'Bold' },
  ];

  const shadowPresets = [
    { label: 'None', value: 'none' },
    { label: 'Small', value: '0 1px 2px 0 rgb(0 0 0 / 0.05)' },
    { label: 'Medium', value: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px 0 rgb(0 0 0 / 0.06)' },
    { label: 'Large', value: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -1px rgb(0 0 0 / 0.06)' },
    { label: 'Extra Large', value: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05)' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-neutral-100 dark:from-neutral-900 dark:via-black dark:to-neutral-950">

      {/* Header */}
      <header className="sticky top-0 z-40 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-lg border-b border-gray-200 dark:border-neutral-800">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Button Generator
            </h1>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <span className="hidden sm:inline">Professional UI Tool</span>
            </div>
          </div>
        </div>
      </header>

      <div className="w-full max-w-[2000px] mx-auto flex flex-col xl:flex-row gap-4 sm:gap-6 lg:gap-8 py-6 sm:py-8 lg:py-12 px-4 sm:px-6 lg:px-8">
        {/* Sidebar Controls */}
        <aside className="w-full xl:w-96 xl:flex-shrink-0 space-y-4 sm:space-y-6 xl:sticky xl:top-24 xl:self-start xl:max-h-[calc(100vh-8rem)] xl:overflow-y-auto xl:pr-2">
          {/* Section: Text */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in">
            <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Button Text</h2>
            <input
              type="text"
              value={config.text}
              onChange={(e) => updateConfig('text', e.target.value)}
              className="w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 dark:border-neutral-700 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-base sm:text-lg font-medium bg-gray-50 dark:bg-neutral-800 transition-all duration-200"
              placeholder="Button label..."
            />
          </section>

          {/* Section: Colors */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in">
            <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Colors</h2>
            {/* Light Colors */}
            <div className="mb-4 sm:mb-6">
              <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2 sm:mb-3 text-xs sm:text-sm uppercase tracking-wider">Light Mode</h3>
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                <ColorInput label="Background" value={config.backgroundColor} onChange={v => updateConfig('backgroundColor', v)} />
                <ColorInput label="Text" value={config.textColor} onChange={v => updateConfig('textColor', v)} />
                <ColorInput label="Border" value={config.borderColor} onChange={v => updateConfig('borderColor', v)} />
                <ColorInput label="Hover BG" value={config.hoverBackgroundColor} onChange={v => updateConfig('hoverBackgroundColor', v)} />
                <ColorInput label="Hover Text" value={config.hoverTextColor} onChange={v => updateConfig('hoverTextColor', v)} />
                <ColorInput label="Hover Border" value={config.hoverBorderColor} onChange={v => updateConfig('hoverBorderColor', v)} />
              </div>
            </div>
            {/* Dark Colors */}
            {config.supportDarkMode && (
              <div>
                <h3 className="font-semibold text-gray-700 dark:text-gray-200 mb-2 sm:mb-3 text-xs sm:text-sm uppercase tracking-wider">Dark Mode</h3>
                <div className="grid grid-cols-1 gap-2 sm:gap-3">
                  <ColorInput label="Background" value={config.darkBackgroundColor} onChange={v => updateConfig('darkBackgroundColor', v)} />
                  <ColorInput label="Text" value={config.darkTextColor} onChange={v => updateConfig('darkTextColor', v)} />
                  <ColorInput label="Border" value={config.darkBorderColor} onChange={v => updateConfig('darkBorderColor', v)} />
                  <ColorInput label="Hover BG" value={config.darkHoverBackgroundColor} onChange={v => updateConfig('darkHoverBackgroundColor', v)} />
                  <ColorInput label="Hover Text" value={config.darkHoverTextColor} onChange={v => updateConfig('darkHoverTextColor', v)} />
                  <ColorInput label="Hover Border" value={config.darkHoverBorderColor} onChange={v => updateConfig('darkHoverBorderColor', v)} />
                </div>
              </div>
            )}
          </section>

          {/* Section: Border & Radius */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in">
            <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Border & Radius</h2>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mb-3 sm:mb-4">
              <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 w-full sm:w-20 font-medium">Border</span>
              <div className="flex items-center gap-2 w-full sm:w-auto">
                <input type="color" value={config.borderColor} onChange={e => updateConfig('borderColor', e.target.value)} className="w-8 h-8 rounded-full border border-gray-300 dark:border-neutral-700 flex-shrink-0" />
                <input type="number" value={config.borderWidth} onChange={e => updateConfig('borderWidth', parseInt(e.target.value))} min="0" max="10" className="w-16 px-2 py-1 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 text-sm" />
                <select value={config.borderStyle} onChange={e => updateConfig('borderStyle', e.target.value)} className="flex-1 sm:flex-none px-2 py-1 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 text-sm">
                  {borderStyles.map(style => (<option key={style} value={style}>{style}</option>))}
                </select>
              </div>
            </div>
            <div className="mb-2">
              <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Radius: {config.borderRadius}px</label>
              <input type="range" min="0" max="50" value={config.borderRadius} onChange={e => updateConfig('borderRadius', parseInt(e.target.value))} className="w-full accent-blue-500 h-2" />
            </div>
          </section>

          {/* Section: Shadow */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in">
            <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Shadow</h2>
            <select value={config.boxShadow} onChange={e => updateConfig('boxShadow', e.target.value)} className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 text-sm sm:text-base">
              {shadowPresets.map(shadow => (<option key={shadow.label} value={shadow.value}>{shadow.label}</option>))}
            </select>
          </section>

          {/* Section: Size & Typography */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in">
            <h2 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-700 dark:text-blue-300 tracking-tight">Size & Typography</h2>
            <div className="space-y-3 sm:space-y-4">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Width: {config.width}px</label>
                <input type="range" min="60" max="400" value={config.width} onChange={e => updateConfig('width', parseInt(e.target.value))} className="w-full accent-blue-500 h-2" />
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Height: {config.height}px</label>
                <input type="range" min="24" max="80" value={config.height} onChange={e => updateConfig('height', parseInt(e.target.value))} className="w-full accent-blue-500 h-2" />
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Font Size: {config.fontSize}px</label>
                <input type="range" min="10" max="32" value={config.fontSize} onChange={e => updateConfig('fontSize', parseInt(e.target.value))} className="w-full accent-blue-500 h-2" />
              </div>
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Font Weight</label>
                <select value={config.fontWeight} onChange={e => updateConfig('fontWeight', e.target.value)} className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-700 rounded-lg bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 text-sm sm:text-base">
                  {fontWeights.map(weight => (<option key={weight.value} value={weight.value}>{weight.label}</option>))}
                </select>
              </div>
            </div>
          </section>

          {/* Section: Dark Mode Toggle */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 animate-fade-in flex items-center justify-between">
            <span className="text-sm sm:text-base font-semibold text-gray-700 dark:text-gray-300">Dark Mode Support</span>
            <button
              onClick={() => updateConfig('supportDarkMode', !config.supportDarkMode)}
              className={`relative inline-flex h-6 w-12 sm:h-7 sm:w-14 items-center rounded-full transition-colors duration-200 ${config.supportDarkMode ? 'bg-blue-600' : 'bg-gray-300'}`}
            >
              <span className={`inline-block h-4 w-4 sm:h-5 sm:w-5 transform rounded-full bg-white shadow transition-transform duration-200 ${config.supportDarkMode ? 'translate-x-6 sm:translate-x-7' : 'translate-x-1'}`} />
            </button>
          </section>
        </aside>

        {/* Main Content: Preview & Export */}
        <main className="flex-1 flex flex-col gap-4 sm:gap-6 lg:gap-8 min-w-0">
          {/* Preview */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 lg:p-8 flex flex-col items-center animate-fade-in">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full mb-4 sm:mb-6 gap-3 sm:gap-0">
              <h2 className="text-xl sm:text-2xl font-bold text-blue-700 dark:text-blue-300 flex items-center gap-2">
                <Eye className="w-5 h-5 sm:w-6 sm:h-6" /> Preview
              </h2>
              {config.supportDarkMode && (
                <div className="flex space-x-1 sm:space-x-2 bg-gray-100 dark:bg-neutral-800 rounded-lg p-1">
                  <button
                    onClick={() => setPreviewMode('light')}
                    className={`px-2 sm:px-3 py-1 rounded text-sm sm:text-base font-medium transition-colors duration-200 ${previewMode === 'light' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                  >
                    <Sun className="w-4 h-4 sm:w-5 sm:h-5" />
                  </button>
                  <button
                    onClick={() => setPreviewMode('dark')}
                    className={`px-2 sm:px-3 py-1 rounded text-sm sm:text-base font-medium transition-colors duration-200 ${previewMode === 'dark' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                  >
                    <Moon className="w-4 h-4 sm:w-5 sm:h-5" />
                  </button>
                </div>
              )}
            </div>
            <div className={`flex items-center justify-center min-h-32 sm:min-h-48 w-full rounded-xl transition-colors duration-200 p-4 ${previewMode === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
              <button
                style={getButtonStyle(false, previewMode === 'dark')}
                onMouseEnter={e => Object.assign(e.currentTarget.style, getButtonStyle(true, previewMode === 'dark'))}
                onMouseLeave={e => Object.assign(e.currentTarget.style, getButtonStyle(false, previewMode === 'dark'))}
                className="transition-all duration-200 max-w-full overflow-hidden"
              >
                {config.text}
              </button>
            </div>
          </section>

          {/* Export Panel */}
          <section className="bg-white dark:bg-neutral-900 rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 dark:border-neutral-800 p-4 sm:p-6 lg:p-8 animate-fade-in">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 sm:mb-6 gap-3 sm:gap-0">
              <h2 className="text-xl sm:text-2xl font-bold text-blue-700 dark:text-blue-300">Export</h2>
              <div className="flex space-x-1 sm:space-x-2 bg-gray-100 dark:bg-neutral-800 rounded-lg p-1 w-full sm:w-auto">
                <button
                  onClick={() => setExportFormat('css')}
                  className={`flex-1 sm:flex-none px-3 sm:px-4 py-1 rounded text-sm sm:text-base font-medium transition-colors duration-200 ${exportFormat === 'css' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                >CSS</button>
                <button
                  onClick={() => setExportFormat('tailwind')}
                  className={`flex-1 sm:flex-none px-3 sm:px-4 py-1 rounded text-sm sm:text-base font-medium transition-colors duration-200 ${exportFormat === 'tailwind' ? 'bg-white text-blue-700 shadow' : 'text-gray-600 hover:text-blue-700'}`}
                >Tailwind</button>
              </div>
            </div>
            {exportFormat === 'tailwind' && (
              <div className="mb-4 p-3 bg-gray-50 dark:bg-neutral-800 rounded-lg flex flex-col sm:flex-row gap-3 sm:gap-6">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="className"
                    checked={tailwindFormat === 'className'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-2 accent-blue-500"
                  />
                  <span className="text-sm">className</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="tailwindFormat"
                    value="class"
                    checked={tailwindFormat === 'class'}
                    onChange={e => setTailwindFormat(e.target.value as 'className' | 'class')}
                    className="mr-2 accent-blue-500"
                  />
                  <span className="text-sm">class</span>
                </label>
              </div>
            )}
            <div className="relative">
              <pre className="bg-gray-900 text-gray-100 p-4 sm:p-6 rounded-xl overflow-x-auto text-xs sm:text-sm lg:text-base max-h-64 sm:max-h-72 lg:max-h-80 transition-all duration-200 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                <code className="whitespace-pre-wrap break-words">{getExportContent()}</code>
              </pre>
              <button
                onClick={() => copyToClipboard(getExportContent())}
                disabled={isGenerating}
                className={`absolute top-2 sm:top-4 right-2 sm:right-4 px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm lg:text-base font-medium flex items-center gap-1 sm:gap-2 transition-all duration-200 transform hover:scale-105 active:scale-95 ${
                  copied
                    ? 'bg-green-600 text-white shadow-lg'
                    : isGenerating
                      ? 'bg-gray-500 text-white cursor-not-allowed'
                      : 'bg-blue-700 hover:bg-blue-800 text-white shadow-md hover:shadow-lg'
                }`}
                aria-label="Copy code"
              >
                <Copy className={`w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 ${isGenerating ? 'animate-spin' : ''}`} />
                <span className="hidden sm:inline">
                  {copied ? 'Copied!' : isGenerating ? 'Copying...' : 'Copy'}
                </span>
              </button>
            </div>
          </section>
        </main>
      </div>

      {/* Footer */}
      <footer className="border-t border-gray-200 dark:border-neutral-800 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm">
        <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Professional Button Generator - Create beautiful, responsive buttons
            </div>
            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-500">
              <span>CSS & Tailwind Export</span>
              <span>•</span>
              <span>Dark Mode Support</span>
              <span>•</span>
              <span>Fully Responsive</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

// Helper: ColorInput
const ColorInput: React.FC<{ label: string; value: string; onChange: (v: string) => void }> = ({ label, value, onChange }) => (
  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3">
    <span className="w-full sm:w-20 lg:w-24 text-xs font-semibold text-gray-600 dark:text-gray-400">{label}</span>
    <div className="flex items-center gap-2 w-full sm:w-auto">
      <input
        type="color"
        value={value}
        onChange={e => onChange(e.target.value)}
        className="w-8 h-8 rounded-full border border-gray-300 dark:border-neutral-700 flex-shrink-0 cursor-pointer"
      />
      <input
        type="text"
        value={value}
        onChange={e => onChange(e.target.value)}
        className="flex-1 sm:w-24 lg:w-28 px-2 py-1 text-xs border border-gray-300 dark:border-neutral-700 rounded font-mono bg-gray-50 dark:bg-neutral-800 text-gray-900 dark:text-gray-100 min-w-0"
        placeholder="#000000"
      />
    </div>
  </div>
);

export default ButtonGenerator;