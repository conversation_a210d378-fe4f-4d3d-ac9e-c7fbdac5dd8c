"use client";

import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "motion/react";

interface LoadingBarProps {
  isLoading: boolean;
  color?: string;
  height?: number;
  duration?: number;
}

export const LoadingBar: React.FC<LoadingBarProps> = ({
  isLoading,
  height = 3,
  duration = 0.6,
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    console.log('Loading bar effect triggered, isLoading:', isLoading); // Debug log
    if (isLoading) {
      console.log('Starting progress animation'); // Debug log
      setProgress(0);
      setIsVisible(true);

      // YouTube-style realistic progress
      const intervals: NodeJS.Timeout[] = [];

      // Fast initial progress
      intervals.push(setTimeout(() => setProgress(25), 50));
      intervals.push(setTimeout(() => setProgress(45), 200));
      intervals.push(setTimeout(() => setProgress(65), 400));
      intervals.push(setTimeout(() => setProgress(80), 700));

      // Slower progress towards the end
      const slowInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) return prev;
          return prev + Math.random() * 3;
        });
      }, 300);
      intervals.push(slowInterval);

      return () => {
        intervals.forEach(clearTimeout);
        clearInterval(slowInterval);
      };
    } else {
      // Complete the loading
      setProgress(100);
      const timeout = setTimeout(() => {
        setProgress(0);
        setIsVisible(false);
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [isLoading]);

  if (!isLoading && !isVisible) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        height: `${height}px`,
        zIndex: 9999,
        pointerEvents: 'none',
        backgroundColor: 'transparent'
      }}
    >
      <AnimatePresence>
        {(isLoading || isVisible) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            style={{
              width: '100%',
              height: '100%',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <motion.div
              initial={{ width: "0%" }}
              animate={{ width: `${progress}%` }}
              transition={{
                duration: isLoading ? duration : 0.4,
                ease: isLoading ? "easeOut" : "easeInOut",
              }}
              style={{
                height: '100%',
                background: 'linear-gradient(90deg, #10b981 0%, #22c55e 30%, #4ade80 50%, #22c55e 70%, #10b981 100%)',
                boxShadow: '0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3)',
                position: 'relative',
                borderRadius: '0 2px 2px 0'
              }}
            >
              {/* Glass shimmer effect */}
              {isLoading && (
                <motion.div
                  animate={{
                    x: ["-100%", "200%"],
                  }}
                  transition={{
                    duration: 1.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '50%',
                    height: '100%',
                    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent)',
                    transform: 'skewX(-20deg)'
                  }}
                />
              )}

              {/* Top glass highlight */}
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '1px',
                  background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent)'
                }}
              />

              {/* Bottom subtle shadow */}
              <div
                style={{
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: '1px',
                  background: 'linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent)'
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
